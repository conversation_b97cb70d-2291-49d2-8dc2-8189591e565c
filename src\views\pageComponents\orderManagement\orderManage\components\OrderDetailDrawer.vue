<template>
  <a-drawer :width="1000" title="采购订单详情" :open="visible" @close="handleClose">
    <template #extra>
      <a-space>
        <a-button :loading="loading" @click="handleChangeInfo('prev')">上一条</a-button>
        <a-button :loading="loading" @click="handleChangeInfo('next')">下一条</a-button>
      </a-space>
    </template>
    <a-spin :spinning="loading">
      <div class="px-20% mb-16px">
        <a-steps :current="selectStep" :items="stepList"></a-steps>
      </div>
      <Form />
      <div class="flex flex-col gap-12px">
        <OrderPurchaseItem v-for="i in form.purchaseOrderDetails" :key="i.id" :info="i" />
        <div class="flex justify-end mb-16">
          <div class="c-#999 w-200 flex flex-col gap-4 lh-20px">
            <div class="flex justify-between">
              <span>商品总数量:</span>
              <span>{{ countTotal }}</span>
            </div>
            <div class="flex justify-between">
              <span>商品总价(含税):</span>
              <span>¥{{ countTotalPrice }}</span>
            </div>
            <div class="flex justify-between">
              <span>其他费用:</span>
              <span>¥{{ countOtherPrice }}</span>
            </div>
            <div class="flex justify-between text-14px c-#333 font-600">
              <span>费用总计：</span>
              <span>¥{{ new Decimal(countTotalPrice).plus(countOtherPrice).toFixed(2) }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="drawer-title">发货&入库明细</div>
      <vxe-table border size="small" :data="form.storeList" class="!text-12" ref="tableRef" :merge-cells="mergeCells">
        <vxe-column type="seq" width="50" align="center"></vxe-column>
        <vxe-column width="350" field="name" title="商品">
          <template #default="{ row }">
            <div class="flex">
              <BaseImage :width="60" :height="60" :src="row.image_url"></BaseImage>
              <div class="flex flex-col ml-4">
                <div class="lh-20">{{ row.sku_name }}</div>
                <div class="lh-20">
                  <span class="c-#999">类目：</span>
                  <span>{{ row.all_category || '--' }}</span>
                </div>
                <div class="lh-20">
                  <span class="c-#999">规格：</span>
                  <span>{{ row.type_specification || '--' }}</span>
                </div>
              </div>
            </div>
          </template>
        </vxe-column>
        <vxe-column width="150" field="sex" title="商品编码">
          <template #default="{ row }">
            <div>
              <div>
                <span class="c-#999">供应商：</span>
                <span>{{ row.srs_supplier_prod_code || '--' }}</span>
              </div>
              <div>
                <span class="c-#999">平台：</span>
                <span>{{ row.srs_platform_prod_code || '--' }}</span>
              </div>
            </div>
          </template>
        </vxe-column>
        <vxe-column width="180" field="number" title="编号（预约入库单编号）"></vxe-column>
        <vxe-column width="260" field="age" title="发货信息">
          <template #default="{ row }">
            <div>
              <div>
                <span class="c-#999">该次发货数（预约入库数）：</span>
                <span>{{ row.scheduled_quantity || '--' }}</span>
              </div>
              <div>
                <span class="c-#999">收货仓库：</span>
                <span>{{ row.warehouse_name || '--' }}</span>
              </div>
            </div>
          </template>
        </vxe-column>
        <vxe-column width="150" field="age" title="物流信息">
          <template #default="{ row }">
            <div>
              <div>
                <span class="c-#999">物流公司：</span>
                <span>{{ row.logistics_company || '--' }}</span>
              </div>
              <div>
                <span class="c-#999">物流单号：</span>
                <span>{{ row.tracking_number || '--' }}</span>
              </div>
            </div>
          </template>
        </vxe-column>
        <vxe-column width="150" field="scheduled_arrival_time" title="预计到货时间" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="booking_purchase_quantity" title="采购入库总数" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="io_id" title="入库单编号" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="purchase_inbound_quantity" title="采购入库数" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="inbound_time" title="入库时间" :formatter="defaultFormatter"></vxe-column>
      </vxe-table>
      <div class="drawer-title mt-16">退库明细</div>
      <vxe-table border size="small" :data="form.returnList" class="!text-12">
        <vxe-column type="seq" width="50" align="center"></vxe-column>
        <vxe-column width="350" field="name" title="商品">
          <template #default="{ row }">
            <div class="flex">
              <BaseImage :width="60" :height="60" :src="row.image_url"></BaseImage>
              <div class="flex flex-col ml-4">
                <div class="lh-20">{{ row.sku_name }}</div>
                <div class="lh-20">
                  <span class="c-#999">类目：</span>
                  <span>{{ row.all_category || '--' }}</span>
                </div>
                <div class="lh-20">
                  <span class="c-#999">规格：</span>
                  <span>{{ row.type_specification || '--' }}</span>
                </div>
              </div>
            </div>
          </template>
        </vxe-column>
        <vxe-column width="150" field="sex" title="商品编码">
          <template #default="{ row }">
            <div>
              <div>
                <span class="c-#999">供应商：</span>
                <span>{{ row.srs_supplier_prod_code || '--' }}</span>
              </div>
              <div>
                <span class="c-#999">平台：</span>
                <span>{{ row.srs_platform_prod_code || '--' }}</span>
              </div>
            </div>
          </template>
        </vxe-column>
        <vxe-column width="180" field="purchase_return_application_number" title="退库申请单编号" :formatter="defaultFormatter" />
        <vxe-column width="150" field="application_type_str" title="退库类型" :formatter="defaultFormatter" />
        <vxe-column width="150" field="return_reason_type_str" title="退库原因" :formatter="defaultFormatter" />
        <vxe-column width="150" field="total_purchase_quantity" title="采购数量" :formatter="defaultFormatter" />
        <vxe-column width="150" field="total_actual_inbound" title="实际到货总数量" :formatter="defaultFormatter" />
        <vxe-column width="150" field="return_amount" title="退库金额" :formatter="priceFormatter" />
        <vxe-column width="150" field="actual_return_quantity" title="实际退库数量" :formatter="defaultFormatter" />
      </vxe-table>
    </a-spin>
  </a-drawer>
</template>

<script setup lang="ts">
import { useBaseForm, type BaseFormItem } from '@/hook/useBaseForm'
import { getOrderDetail, getStoreDetailList, getReturnDetailList } from '@/servers/OrderManage'
import { message } from 'ant-design-vue'
import Decimal from 'decimal.js'
import { withDirectives } from 'vue'
import { vCopy } from '@/directive/copy'
import { defaultFormatter, priceFormatter } from '@/utils/VxeUi'
import { VxeTable, VxeTablePropTypes } from 'vxe-table'
import BaseImage from '@/components/BaseImage/index.vue'
import { CopyOutlined } from '@ant-design/icons-vue'
import OrderPurchaseItem from './OrderPurchaseItem.vue'

const visible = ref(false)
// 加载状态
const loading = ref(false)
// 当前选中的id
const selectId = ref()
// 当前选中的供应商子id
const selectCompanySupplierId = ref()
// 合并单元格
const mergeCells = ref<VxeTablePropTypes.MergeCells>([])
const tableRef = useTemplateRef<InstanceType<typeof VxeTable>>('tableRef')

const tableIds = ref<number[]>([])
// 表单
const form = ref<any>({
  storeList: [],
  returnList: [],
  purchaseOrderDetails: [],
})
// 当前步骤条
const selectStep = ref(0)
// 步骤条列表
const stepList = [
  {
    title: '未发货',
  },
  {
    title: '已发货/部分发货',
  },
  {
    title: '已完成',
  },
]

// 计算商品总数量
const countTotal = computed(() => {
  return new Decimal(form.value.purchaseOrderDetails.reduce((acc, curr) => acc + (curr.purchase_quantity || 0), 0)).toNumber()
})

// 计算商品总价
const countTotalPrice = computed(() => {
  return new Decimal(form.value.purchaseOrderDetails.reduce((acc, curr) => acc + (curr.purchase_tax_price || 0) * (curr.purchase_quantity || 0), 0)).toFixed(2)
})

// 计算其他费用
const countOtherPrice = computed(() => {
  return new Decimal(form.value.purchaseOrderDetails.reduce((acc, curr) => acc + (curr.other_fees || 0), 0)).toFixed(2)
})

// 获取详情
const getDetail = (id: number, company_supplier_id: number) => {
  const params = { id, company_supplier_id }
  loading.value = true
  Promise.all([getOrderDetail(params), getStoreDetailList(params), getReturnDetailList(params)])
    .then(([basicRes, storeRes, returnRes]) => {
      if (basicRes.data.order_status === 2) {
        selectStep.value = 2
      } else if ([20, 30].includes(basicRes.data.shipment_status)) {
        selectStep.value = 1
      } else {
        selectStep.value = 0
      }
      basicRes.data.shipments_address = `${basicRes.data.province}${basicRes.data.city}${basicRes.data.area}${basicRes.data.shipments_address}`
      form.value = {
        ...basicRes.data,
        storeList: mergeTableCells(storeRes.data),
        returnList: returnRes.data,
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 合并
const mergeTableCells = (data: any) => {
  const columns = tableRef.value?.getColumns() || []
  const mergeList: any[] = []
  mergeCells.value = []
  let findIndex = 0
  const list = data.flatMap((item: any, index: number) => {
    if (item.purchase_in_relation_infos?.length === 0) {
      return item
    }
    if (item.purchase_in_relation_infos?.length > 1) {
      // 为每个需要合并的列创建合并规则
      columns.forEach((col, colIndex) => {
        const mergeKeys = ['io_id', 'purchase_inbound_quantity', 'inbound_time']
        if (!mergeKeys.includes(col.field)) {
          mergeList.push({
            row: index + findIndex,
            col: colIndex,
            rowspan: item.purchase_in_relation_infos.length,
            colspan: 1,
          })
        }
      })
      findIndex += item.purchase_in_relation_infos.length - 1
    }
    return item.purchase_in_relation_infos.map((product: any) => {
      return {
        ...item,
        ...product,
        id: product.io_id,
      }
    })
  })
  nextTick(() => {
    mergeCells.value = mergeList
  })
  return list
}

// 关闭
const handleClose = () => {
  visible.value = false
}
// 显示
const handleShow = (id: number, company_supplier_id: number, ids: number[]) => {
  selectId.value = id
  selectCompanySupplierId.value = company_supplier_id
  visible.value = true
  tableIds.value = ids
  getDetail(id, company_supplier_id)
}

// 上一条
const handleChangeInfo = (type: 'prev' | 'next') => {
  const index = tableIds.value.findIndex((id) => id === selectId.value)
  if (index === 0 && type === 'prev') {
    message.error('已经是第一条')
    return
  }
  if (index === tableIds.value.length - 1 && type === 'next') {
    message.error('已经是最后一条')
    return
  }

  if (type === 'prev') {
    selectId.value = tableIds.value[index - 1]
  } else {
    selectId.value = tableIds.value[index + 1]
  }
  getDetail(selectId.value, selectCompanySupplierId.value)
}

const formArr = ref<BaseFormItem[]>([
  {
    type: 'title',
    label: '采购基本信息:',
  },
  {
    label: () => h('div', {}, [h('span', {}, '采购订单编号:'), form.value.number && withDirectives(h(CopyOutlined, { class: 'ml-4 c-primary cursor-pointer' }), [[vCopy, form.value.number]])]),
    key: 'number',
    type: 'text',
    span: 6,
  },
  {
    label: '供应商:',
    key: 'supplier_name',
    type: 'text',
    span: 6,
  },
  {
    label: '采购类型:',
    key: 'type_str',
    type: 'text',
    span: 6,
  },
  {
    label: '结算方式:',
    key: 'settlement_type_str',
    type: 'text',
    span: 6,
  },
  {
    label: '收货仓库:',
    key: 'warehourse_name',
    type: 'text',
    span: 6,
  },
  {
    label: '采购员:',
    key: 'buyer_name',
    type: 'text',
    span: 6,
  },
  {
    label: '采购时间:',
    key: 'purchase_time',
    type: 'text',
    span: 6,
  },
  {
    label: '订单状态:',
    key: 'order_status_str',
    type: 'text',
    span: 6,
  },
  {
    label: '发货状态:',
    key: 'shipment_status_str',
    type: 'text',
    span: 6,
  },
  {
    label: '收货状态:',
    key: 'purchase_status_str',
    type: 'text',
    span: 6,
  },
  {
    label: () =>
      h('div', {}, [
        h('span', {}, '收货信息'),
        withDirectives(h(CopyOutlined, { class: 'ml-4 c-primary cursor-pointer' }), [
          [vCopy, () => `收货人：${form.value.consignee || ''}，联系电话：${form.value.phone_number || ''}，收货地址：${form.value.shipments_address || ''}`],
        ]),
      ]),
    type: 'title',
  },
  {
    label: '收货人:',
    key: 'consignee',
    type: 'text',
    span: 6,
  },
  {
    label: '联系电话:',
    key: 'phone_number',
    type: 'text',
    span: 6,
  },
  {
    label: '收货地址:',
    key: 'shipments_address',
    type: 'text',
    span: 12,
  },
  {
    label: '采购商品',
    type: 'title',
  },
])

const [Form] = useBaseForm({
  formConfig: formArr,
  modelValue: form,
  isText: true,
})

defineExpose({
  show: handleShow,
})
</script>

<style scoped lang="scss"></style>
